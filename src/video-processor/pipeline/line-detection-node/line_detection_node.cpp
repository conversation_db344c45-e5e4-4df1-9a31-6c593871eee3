#include "./line_detection_node.h"
#include "../../helpers/helpers.h"

#include "./devtools/data_exporter.hpp"
#include "./partials/sync-orchestrator/sync-encoders/ntsc-encoder.h"

namespace IQVideoProcessor::Pipeline {

std::vector<TFloat> syncPositionsGraphic;
std::vector<TFloat> orcPositionsGraphic;

LineDetectionNode::LineDetectionNode(const SampleRateType sampleRate) : sampleRate_(sampleRate), segmentSyncDetectionFilter_(sampleRate) {
  setRunning();
}

LineDetectionNode::~LineDetectionNode() {
  PipelineComponent::stop();
}

bool LineDetectionNode::process(DemodulatedSegment& segment) {
  if (!running()) return false;

  auto& filteredSegment = segmentSyncDetectionFilter_.process(segment);
  auto [videoSyncPulses, processingEndPosition] = segmentPulsesDetector_.process(filteredSegment);

  // <DEBUGGING>
  if (syncPositionsGraphic.size() != segment.totalSamples) {
    syncPositionsGraphic.resize(segment.totalSamples);
    orcPositionsGraphic.resize(segment.totalSamples);
  }
  std::fill(syncPositionsGraphic.begin(), syncPositionsGraphic.end(), 0);
  std::fill(orcPositionsGraphic.begin(), orcPositionsGraphic.end(), 30);
  for (const auto & detected_video_sync_pulse : videoSyncPulses) {
    // auto centerPos = static_cast<uint32_t>(detected_video_sync_pulse.centerPosition) + segment.effectiveOffset - segment.effectiveStartPosition;
    auto fallingPos = static_cast<uint32_t>(detected_video_sync_pulse.fallingFrontPosition) + segment.effectiveOffset;
    auto risingPos = static_cast<uint32_t>(detected_video_sync_pulse.risingFrontPosition) + segment.effectiveOffset;
    auto centerPos = static_cast<uint32_t>(detected_video_sync_pulse.absCenterPosition) - segment.effectiveStartPosition + segment.effectiveOffset;
    syncPositionsGraphic[fallingPos] = -35;
    syncPositionsGraphic[centerPos] = -20;
    syncPositionsGraphic[risingPos] = -35;
  }
  DevTools::export_debug_data<TFloat>("LDN", "fronts", segment.segmentIndex, syncPositionsGraphic.data(), syncPositionsGraphic.size());
  DevTools::export_debug_data<TFloat>("LDN", "original", segment.segmentIndex, segment.data.data(), segment.totalSamples);
  DevTools::export_debug_data<TFloat>("LDN", "ave500kHz", filteredSegment.segmentIndex, filteredSegment.data.data(), filteredSegment.totalSamples);
  // </DEBUGGING>

  const auto& standardDetectionResult = videoStandardDetector_.processSegmentSyncPulses(videoSyncPulses);
  if (standardDetectionResult.status == VideoStandardDetector::DETECTION_IN_PROGRESS) {
    return running();
  }

  if (standardDetectionResult.status == VideoStandardDetector::DETECTION_FAILED) {
    if (!standardDetectorRetryCountdown_.running()) {
      standardDetectorRetryCountdown_.reset();
    }
    // Waiting for ~1 second before retrying to detect the standard again
    if (standardDetectorRetryCountdown_.tick(filteredSegment.effectiveSamples)) {
      videoStandardDetector_.reset();
      standardDetectorRetryCountdown_.stop(); // just in case
    }
    return running();
  }

  if (standardDetectionResult.status == VideoStandardDetector::DETECTION_COMPLETE) {
    // Successfully detected the standard, no further action needed
    standardDetectorRetryCountdown_.stop(); // Stop further retries

    if (!syncOrchestrator_.initialized()) {
      // Initialize the orchestrator only once when standard is first detected
      syncOrchestrator_.initialize(standardDetectionResult, [filteredSegment](const SyncOrchestrator::EventData &event) {
        std::cout << "SyncOrchestrator Event: Type=" << event.type
                  << ", Standard=" << event.standard
                  << ", IsOdd=" << event.isOdd
                  << ", LineDistance=" << event.lineDistance_
                  << ", LineNumber=" << event.lineNumber
                  << ", FromPosition=" << event.fromAbsPosition_
                  << ", ToPosition=" << event.toAbsPosition_
                  << ", UninterruptedFrames=" << event.uninterruptedFrameSeries
                  << std::endl;

        switch (event.type) {
          case SyncOrchestrator::FRAME_BEGIN:
            std::cout << "  >> FRAME BEGIN: " << (event.isOdd ? "ODD" : "EVEN") << " field, "
                      << "uninterrupted frames: " << event.uninterruptedFrameSeries << std::endl;
            break;
          case SyncOrchestrator::EQUALIZATION_DETECTED: {
            std::cout << "  >> EQUALIZATION DETECTED: " << (event.isOdd ? "ODD" : "EVEN") << " field at " << event.fromAbsPosition_
                      << " to " << event.toAbsPosition_ << " (distance: " << event.lineDistance_ << " samples)" << std::endl;
            // Mark equalization pulses in debug graphic
            auto sampleCount = static_cast<size_t>(event.toAbsPosition_ - event.fromAbsPosition_);
            for (auto i = 0; i < sampleCount; ++i) {
              auto pos = static_cast<uint32_t>(event.fromAbsPosition_) - filteredSegment.effectiveStartPosition + i + filteredSegment.effectiveOffset;
              if (pos < orcPositionsGraphic.size()) {
                orcPositionsGraphic[pos] = 10;
              } else {
                orcPositionsGraphic[orcPositionsGraphic.size() - 1] = 30;
                // throw std::out_of_range("Position out of range in equalization marking");
              }
            }
            break;
          }
          case SyncOrchestrator::LINE_DETECTED: {
            std::cout << "  >> LINE DETECTED: " << (event.isOdd ? "ODD" : "EVEN") << " field, line #" << event.lineNumber
                      << " at " << event.fromAbsPosition_ << " to " << event.toAbsPosition_ << std::endl;
            // Mark video lines in debug graphic
            auto sampleCount = static_cast<size_t>(event.toAbsPosition_ - event.fromAbsPosition_);
            for (auto i = 0; i < sampleCount; ++i) {
              auto pos = static_cast<uint32_t>(event.fromAbsPosition_) - filteredSegment.effectiveStartPosition + i + filteredSegment.effectiveOffset;
              if (pos < orcPositionsGraphic.size()) {
                orcPositionsGraphic[pos] = -10;
              } else {
                orcPositionsGraphic[orcPositionsGraphic.size() - 1] = 30;
                // throw std::out_of_range("Position out of range in equalization marking");
              }
            }
            break;
          }
          case SyncOrchestrator::FRAME_END:
            std::cout << "  >> FRAME END: " << (event.isOdd ? "ODD" : "EVEN") << " field" << std::endl;
            break;
          case SyncOrchestrator::UNKNOWN_EVENT:
          default:
            std::cout << "  >> Unknown event type: " << event.type << std::endl;
            break;
        }
      });
    }
  }

  if (syncOrchestrator_.initialized()) {
    syncOrchestrator_.process(videoSyncPulses, processingEndPosition);
    DevTools::export_debug_data<TFloat>("LDN", "orcResults", filteredSegment.segmentIndex, orcPositionsGraphic.data(), orcPositionsGraphic.size());
  }

  return running();
}

} // namespace IQVideoProcessor::Pipeline
