#include "./line_detection_node.h"
#include "../../helpers/helpers.h"

#include "./devtools/data_exporter.hpp"
#include "./partials/sync-orchestrator/sync-encoders/ntsc-encoder.h"

namespace IQVideoProcessor::Pipeline {

std::vector<TFloat> syncPositionsGraphic;
std::vector<TFloat> orcPositionsGraphic;

LineDetectionNode::LineDetectionNode(const SampleRateType sampleRate) : sampleRate_(sampleRate), segmentSyncDetectionFilter_(sampleRate) {
  setRunning();
}

LineDetectionNode::~LineDetectionNode() {
  PipelineComponent::stop();
}

bool LineDetectionNode::process(DemodulatedSegment& segment) {
  if (!running()) return false;

  auto& filteredSegment = segmentSyncDetectionFilter_.process(segment);
  auto [videoSyncPulses, processingEndPosition] = segmentPulsesDetector_.process(filteredSegment);

  // <DEBUGGING>
  if (syncPositionsGraphic.size() != segment.totalSamples) {
    syncPositionsGraphic.resize(segment.totalSamples);
    orcPositionsGraphic.resize(segment.totalSamples);
  }
  std::fill(syncPositionsGraphic.begin(), syncPositionsGraphic.end(), 0);
  std::fill(orcPositionsGraphic.begin(), orcPositionsGraphic.end(), 30);
  for (const auto & detected_video_sync_pulse : videoSyncPulses) {
    // auto centerPos = static_cast<uint32_t>(detected_video_sync_pulse.centerPosition) + segment.effectiveOffset - segment.effectiveStartPosition;
    auto fallingPos = static_cast<uint32_t>(detected_video_sync_pulse.fallingFrontPosition) + segment.effectiveOffset;
    auto risingPos = static_cast<uint32_t>(detected_video_sync_pulse.risingFrontPosition) + segment.effectiveOffset;
    auto centerPos = static_cast<uint32_t>(detected_video_sync_pulse.absCenterPosition) - segment.effectiveStartPosition + segment.effectiveOffset;
    syncPositionsGraphic[fallingPos] = -35;
    syncPositionsGraphic[centerPos] = -20;
    syncPositionsGraphic[risingPos] = -35;
  }
  DevTools::export_debug_data<TFloat>("LDN", "fronts", segment.segmentIndex, syncPositionsGraphic.data(), syncPositionsGraphic.size());
  DevTools::export_debug_data<TFloat>("LDN", "original", segment.segmentIndex, segment.data.data(), segment.totalSamples);
  DevTools::export_debug_data<TFloat>("LDN", "ave500kHz", filteredSegment.segmentIndex, filteredSegment.data.data(), filteredSegment.totalSamples);
  // </DEBUGGING>

  const auto& standardDetectionResult = videoStandardDetector_.processSegmentSyncPulses(videoSyncPulses);
  if (standardDetectionResult.status == VideoStandardDetector::DETECTION_IN_PROGRESS) {
    return running();
  }

  if (standardDetectionResult.status == VideoStandardDetector::DETECTION_FAILED) {
    if (!standardDetectorRetryCountdown_.running()) {
      standardDetectorRetryCountdown_.reset();
    }
    // Waiting for ~1 second before retrying to detect the standard again
    if (standardDetectorRetryCountdown_.tick(filteredSegment.effectiveSamples)) {
      videoStandardDetector_.reset();
      standardDetectorRetryCountdown_.stop(); // just in case
    }
    return running();
  }

  if (standardDetectionResult.status == VideoStandardDetector::DETECTION_COMPLETE) {
    // Successfully detected the standard, no further action needed
    standardDetectorRetryCountdown_.stop(); // Stop further retries

    if (!syncOrchestrator_.initialized()) {
      // Initialize the orchestrator only once when standard is first detected
      syncOrchestrator_.initialize(standardDetectionResult, [filteredSegment](const SyncOrchestrator::EventData &event) {
        std::cout << "VideoSyncOrchestrator Event: Type=" << event.type
                  << ", FrameWidth=" << event.frameWidth
                  << ", FrameHeight=" << event.frameHeight
                  << ", PulseNumber=" << event.pulseNumber
                  << ", LineNumber=" << event.lineNumber
                  << ", FromPosition=" << event.fromPosition
                  << ", SampleCount=" << event.sampleCount
                  << std::endl;

        switch (event.type) {
          case SyncOrchestrator::FRAME_BEGIN:
            std::cout << "  >> FRAME BEGIN: " << event.frameWidth << "x" << event.frameHeight << std::endl;
            break;
          case SyncOrchestrator::EQUALIZATION_DETECTED:
            std::cout << "  >> EQ Pulse #" << event.pulseNumber << " at " << event.fromPosition
                      << " (" << event.sampleCount << " samples)" << std::endl;
            for (auto i = 0 ; i < event.sampleCount; ++i) {
              auto pos = static_cast<uint32_t>(event.fromPosition) - filteredSegment.effectiveStartPosition + i + filteredSegment.effectiveOffset;
              orcPositionsGraphic[pos] = 10;
            }
            break;
          case SyncOrchestrator::LINE_DETECTED:
            std::cout << "  >> Line #" << event.lineNumber << " at " << event.fromPosition
                      << " (" << event.sampleCount << " samples)" << std::endl;
            for (auto i = 0 ; i < event.sampleCount; ++i) {
              auto pos = static_cast<uint32_t>(event.fromPosition) - filteredSegment.effectiveStartPosition + i + filteredSegment.effectiveOffset;
              orcPositionsGraphic[pos] = -10;
            }
            break;
          case SyncOrchestrator::FRAME_END:
            std::cout << "  >> FRAME END" << std::endl;
            break;
          case SyncOrchestrator::UNKNOWN_EVENT:
          default:
            std::cout << "  >> Unknown event type" << std::endl;
            break;
        }
      });
    }
  }

  if (syncOrchestrator_.initialized()) {
    syncOrchestrator_.process(videoSyncPulses, processingEndPosition);
    DevTools::export_debug_data<TFloat>("LDN", "orcResults", filteredSegment.segmentIndex, orcPositionsGraphic.data(), orcPositionsGraphic.size());
  }

  return running();
}

} // namespace IQVideoProcessor::Pipeline
