#pragma once
#include "../../../../../types.h"
#include "../../video_sync_pulse_type.h"
#include "../../../../../utils/range/range.h"
#include "types.h"

#include <cassert>
#include <vector>

namespace IQVideoProcessor::Pipeline::SyncEncoders {

enum class Transition {
  NEXT,
  TO_ODD_PRE_EQUALIZING_LAST,
  TO_ODD_VERTICAL_START,
  TO_ODD_VERTICAL_LAST,
  TO_ODD_HORIZONTAL_START,
  TO_EVEN_HORIZONTAL_START,
};

struct EIFuture {
  VideoSyncPulseType pulseType;
  Range<double> inRange;
  double defaultDistance;
  Transition transition;
};

class EncoderItem {
public:
  int32_t id;
  VideoSyncPulseType pulseType;
  size_t defaultFutureIdx;
  std::vector<EIFuture> futures;
  double maxFutureDistance{0};
  bool visible;
  bool odd;

  EncoderItem(const VideoSyncPulseType pt, const bool isVisible, const bool isOdd, std::vector<EIFuture> f, const size_t defaultIdx = 0) {
    assert(!f.empty());
    id = nextItemId();
    pulseType = pt;
    futures = std::move(f);
    defaultFutureIdx = defaultIdx;
    maxFutureDistance = 0;
    visible = isVisible;
    odd = isOdd;
    for (const auto& ef : futures) {
      if (ef.inRange.to > maxFutureDistance) {
        maxFutureDistance = ef.inRange.to;
      }
    }
  }

  [[nodiscard]] inline bool canEstimateFutureByDistance(const double distance) const {
    return distance >= maxFutureDistance;
  }

private:
  static inline int32_t globalItemId_ = 0;
  static int32_t nextItemId() { return globalItemId_++; }
  static void resetItemId() { globalItemId_ = 0; }
};

class VideoSyncEncoder {
public:
  struct Config {
    VideoStandard standard{UNKNOWN_VIDEO_STANDARD};
    SampleRateType sampleRate;
  };

  explicit VideoSyncEncoder(const Config & config) : config_(config) {}
  virtual ~VideoSyncEncoder() = default;

  [[nodiscard]] const EncoderItem& currentItem() const { return getEncoders()[getEncoderIdx()]; }
  [[nodiscard]] const EncoderItem& nextItem() const { return getEncoders()[getNextEncoderIdx()]; }
  virtual void transit(Transition transition) = 0;

  void reset() { setEncoderIdx(0); }

protected:
  [[nodiscard]] inline size_t getEncoderIdx() const { return encoderIdx_; }
  [[nodiscard]] inline size_t getNextEncoderIdx() const {
    const auto idx = encoderIdx_ + 1;
    return idx >= encoders_.size() ? 0 : idx;
  }
  inline void setEncoderIdx(const size_t idx) { encoderIdx_ = idx; }
  [[nodiscard]] inline const std::vector<EncoderItem>& getEncoders() const { return encoders_; }
  [[nodiscard]] inline std::vector<EncoderItem>& getWritableEncoders() { return encoders_; }

  void transitNext() { setEncoderIdx(getNextEncoderIdx()); }

  Config config_;

private:
  size_t encoderIdx_{0};
  std::vector<EncoderItem> encoders_;
};

}